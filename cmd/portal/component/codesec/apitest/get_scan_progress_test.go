package apitest

import (
	"sync"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/codesec"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/store"
)

func TestGetScanProgress(t *testing.T) {
	convey.Convey("测试 GetScanProgress 方法", t, func() {
		// 创建 ScanAPISer 实例
		scanDal := store.NewCodesecScanDao(nil)  // 传入 nil，因为这个测试不需要数据库操作
		projectDal := store.NewProjectStore(nil) // 传入 nil，因为这个测试不需要数据库操作

		scanAPISer := codesec.NewScanAPISer(
			BaseURL,
			AccessKey,
			AccessSecret,
			scanDal,
			projectDal,
		)

		convey.Convey("当 projectUuid 为空时", func() {
			result, err := scanAPISer.GetScanProgress("", TaskId)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "projectUuid cannot be empty")
			convey.So(result, convey.ShouldBeNil)
		})

		convey.Convey("当 appId 为空时", func() {
			result, err := scanAPISer.GetScanProgress(ProjectUuid, "")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "appId cannot be empty")
			convey.So(result, convey.ShouldBeNil)
		})

		convey.Convey("当参数都正确时", func() {
			// 跳过测试如果环境变量未设置
			if ProjectUuid == "" || TaskId == "" || AccessKey == "" || AccessSecret == "" {
				t.Skip("跳过测试：请设置环境变量 CODESEC_PROJECT_UUID, CODESEC_TASK_ID, CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
				return
			}

			result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

			convey.Convey("应该成功返回结果", func() {
				if err != nil {
					t.Logf("API 调用失败: %v", err)
					// 如果是鉴权错误，说明签名算法有问题
					if err.Error() == "API error: 鉴权出错" {
						t.Error("检测到鉴权错误，签名算法需要修复")
						convey.So(err, convey.ShouldBeNil) // 这会导致测试失败，提醒需要修复
					} else {
						// 其他错误也记录下来，但不一定是代码问题
						t.Logf("其他错误: %v", err)
						convey.So(err, convey.ShouldBeNil) // 期望没有错误
					}
				} else {
					// 成功的情况
					convey.So(err, convey.ShouldBeNil)
					convey.So(result, convey.ShouldNotBeNil)
					convey.So(result.Progress, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(result.Progress, convey.ShouldBeLessThanOrEqualTo, 100)
					convey.So(result.Msg, convey.ShouldNotBeEmpty)

					t.Logf("扫描进度: %d%%", result.Progress)
					t.Logf("扫描消息: %s", result.Msg)
					if result.CommitId != "" {
						t.Logf("CommitId: %s", result.CommitId)
					}
					if result.ScanTime != "" {
						t.Logf("扫描开始时间: %s", result.ScanTime)
					}
					if result.FinishTime != "" {
						t.Logf("扫描结束时间: %s", result.FinishTime)
					}
					if result.SpendTime != "" {
						t.Logf("扫描耗时: %s", result.SpendTime)
					}
				}
			})
		})

		convey.Convey("当使用无效的 projectUuid 和 taskId 时", func() {
			// 跳过测试如果环境变量未设置
			if AccessKey == "" || AccessSecret == "" {
				t.Skip("跳过测试：请设置环境变量 CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
				return
			}

			result, err := scanAPISer.GetScanProgress("invalid-project-uuid", "invalid-task-id")

			convey.Convey("应该返回错误", func() {
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(result, convey.ShouldBeNil)
				t.Logf("预期的错误: %v", err)
			})
		})
	})
}

// TestGetScanProgressContinuous 测试连续多次请求 GetScanProgress 方法
func TestGetScanProgressContinuous(t *testing.T) {
	convey.Convey("测试连续多次请求 GetScanProgress 方法", t, func() {
		// 跳过测试如果环境变量未设置
		if ProjectUuid == "" || TaskId == "" || AccessKey == "" || AccessSecret == "" {
			t.Skip("跳过测试：请设置环境变量 CODESEC_PROJECT_UUID, CODESEC_TASK_ID, CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
			return
		}

		// 创建 ScanAPISer 实例
		scanDal := store.NewCodesecScanDao(nil)
		projectDal := store.NewProjectStore(nil)

		scanAPISer := codesec.NewScanAPISer(
			BaseURL,
			AccessKey,
			AccessSecret,
			scanDal,
			projectDal,
		)

		convey.Convey("连续请求10次", func() {
			successCount := 0
			errorCount := 0
			var lastError error

			for i := 0; i < 10; i++ {
				t.Logf("第 %d 次请求", i+1)

				result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

				if err != nil {
					errorCount++
					lastError = err
					t.Logf("第 %d 次请求失败: %v", i+1, err)
				} else {
					successCount++
					t.Logf("第 %d 次请求成功: 进度=%d%%, 消息=%s", i+1, result.Progress, result.Msg)
				}

				// 每次请求之间稍微等待，避免过于频繁
				if i < 9 { // 最后一次不需要等待
					time.Sleep(100 * time.Millisecond)
				}
			}

			t.Logf("总结: 成功 %d 次, 失败 %d 次", successCount, errorCount)

			convey.Convey("应该大部分请求成功", func() {
				// 允许少量失败，但大部分应该成功
				convey.So(successCount, convey.ShouldBeGreaterThan, 5)
				if errorCount > 0 {
					t.Logf("最后一个错误: %v", lastError)
				}
			})
		})

		convey.Convey("并发请求5次", func() {
			var wg sync.WaitGroup
			var mu sync.Mutex
			successCount := 0
			errorCount := 0
			errors := make([]error, 0)

			for i := 0; i < 5; i++ {
				wg.Add(1)
				go func(index int) {
					defer wg.Done()

					t.Logf("并发请求 %d 开始", index+1)
					result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

					mu.Lock()
					if err != nil {
						errorCount++
						errors = append(errors, err)
						t.Logf("并发请求 %d 失败: %v", index+1, err)
					} else {
						successCount++
						t.Logf("并发请求 %d 成功: 进度=%d%%, 消息=%s", index+1, result.Progress, result.Msg)
					}
					mu.Unlock()
				}(i)
			}

			wg.Wait()
			t.Logf("并发测试总结: 成功 %d 次, 失败 %d 次", successCount, errorCount)

			convey.Convey("并发请求应该大部分成功", func() {
				// 并发情况下允许更多失败
				convey.So(successCount, convey.ShouldBeGreaterThan, 2)
				if errorCount > 0 {
					t.Logf("并发错误详情:")
					for i, err := range errors {
						t.Logf("  错误 %d: %v", i+1, err)
					}
				}
			})
		})
	})
}

// TestGetScanProgressStress 压力测试 GetScanProgress 方法
func TestGetScanProgressStress(t *testing.T) {
	convey.Convey("压力测试 GetScanProgress 方法", t, func() {
		// 跳过测试如果环境变量未设置
		if ProjectUuid == "" || TaskId == "" || AccessKey == "" || AccessSecret == "" {
			t.Skip("跳过测试：请设置环境变量 CODESEC_PROJECT_UUID, CODESEC_TASK_ID, CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
			return
		}

		// 创建 ScanAPISer 实例
		scanDal := store.NewCodesecScanDao(nil)
		projectDal := store.NewProjectStore(nil)

		scanAPISer := codesec.NewScanAPISer(
			BaseURL,
			AccessKey,
			AccessSecret,
			scanDal,
			projectDal,
		)

		convey.Convey("快速连续请求50次（无延迟）", func() {
			successCount := 0
			errorCount := 0
			errors := make([]error, 0)

			for i := 0; i < 50; i++ {
				result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

				if err != nil {
					errorCount++
					errors = append(errors, err)
					t.Logf("第 %d 次请求失败: %v", i+1, err)
				} else {
					successCount++
					if i%10 == 0 { // 每10次打印一次
						t.Logf("第 %d 次请求成功: 进度=%d%%", i+1, result.Progress)
					}
				}
			}

			t.Logf("快速连续测试总结: 成功 %d 次, 失败 %d 次", successCount, errorCount)

			if errorCount > 0 {
				t.Logf("错误详情:")
				for i, err := range errors {
					t.Logf("  错误 %d: %v", i+1, err)
				}
			}

			convey.Convey("应该有一定的成功率", func() {
				// 在压力测试下，允许一些失败，但成功率应该超过70%
				successRate := float64(successCount) / 50.0
				t.Logf("成功率: %.2f%%", successRate*100)
				convey.So(successRate, convey.ShouldBeGreaterThan, 0.7)
			})
		})

		convey.Convey("高并发请求20次", func() {
			var wg sync.WaitGroup
			var mu sync.Mutex
			successCount := 0
			errorCount := 0
			errors := make([]error, 0)

			for i := 0; i < 20; i++ {
				wg.Add(1)
				go func(index int) {
					defer wg.Done()

					result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

					mu.Lock()
					if err != nil {
						errorCount++
						errors = append(errors, err)
						t.Logf("并发请求 %d 失败: %v", index+1, err)
					} else {
						successCount++
						if index%5 == 0 { // 每5次打印一次
							t.Logf("并发请求 %d 成功: 进度=%d%%", index+1, result.Progress)
						}
					}
					mu.Unlock()
				}(i)
			}

			wg.Wait()
			t.Logf("高并发测试总结: 成功 %d 次, 失败 %d 次", successCount, errorCount)

			if errorCount > 0 {
				t.Logf("并发错误详情:")
				for i, err := range errors {
					t.Logf("  错误 %d: %v", i+1, err)
				}
			}

			convey.Convey("高并发下应该有合理的成功率", func() {
				// 高并发下允许更多失败，但成功率应该超过60%
				successRate := float64(successCount) / 20.0
				t.Logf("并发成功率: %.2f%%", successRate*100)
				convey.So(successRate, convey.ShouldBeGreaterThan, 0.6)
			})
		})
	})
}

// TestGetScanProgressWithNewInstances 测试每次都创建新实例的情况
func TestGetScanProgressWithNewInstances(t *testing.T) {
	convey.Convey("测试每次都创建新实例的 GetScanProgress 方法", t, func() {
		// 跳过测试如果环境变量未设置
		if ProjectUuid == "" || TaskId == "" || AccessKey == "" || AccessSecret == "" {
			t.Skip("跳过测试：请设置环境变量 CODESEC_PROJECT_UUID, CODESEC_TASK_ID, CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
			return
		}

		convey.Convey("每次请求都创建新实例（模拟真实使用场景）", func() {
			successCount := 0
			errorCount := 0
			errors := make([]error, 0)

			for i := 0; i < 30; i++ {
				// 每次都创建新的实例，模拟真实使用场景
				scanDal := store.NewCodesecScanDao(nil)
				projectDal := store.NewProjectStore(nil)

				scanAPISer := codesec.NewScanAPISer(
					BaseURL,
					AccessKey,
					AccessSecret,
					scanDal,
					projectDal,
				)

				result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

				if err != nil {
					errorCount++
					errors = append(errors, err)
					t.Logf("第 %d 次请求失败: %v", i+1, err)
				} else {
					successCount++
					if i%5 == 0 { // 每5次打印一次
						t.Logf("第 %d 次请求成功: 进度=%d%%", i+1, result.Progress)
					}
				}

				// 稍微等待，模拟真实使用间隔
				time.Sleep(50 * time.Millisecond)
			}

			t.Logf("新实例测试总结: 成功 %d 次, 失败 %d 次", successCount, errorCount)

			if errorCount > 0 {
				t.Logf("错误详情:")
				for i, err := range errors {
					t.Logf("  错误 %d: %v", i+1, err)
				}
			}

			convey.Convey("应该有合理的成功率", func() {
				// 新实例创建可能会有更多问题，但成功率应该超过80%
				successRate := float64(successCount) / 30.0
				t.Logf("成功率: %.2f%%", successRate*100)
				convey.So(successRate, convey.ShouldBeGreaterThan, 0.8)
			})
		})

		convey.Convey("快速创建多个实例并发请求", func() {
			var wg sync.WaitGroup
			var mu sync.Mutex
			successCount := 0
			errorCount := 0
			errors := make([]error, 0)

			for i := 0; i < 15; i++ {
				wg.Add(1)
				go func(index int) {
					defer wg.Done()

					// 每个 goroutine 都创建自己的实例
					scanDal := store.NewCodesecScanDao(nil)
					projectDal := store.NewProjectStore(nil)

					scanAPISer := codesec.NewScanAPISer(
						BaseURL,
						AccessKey,
						AccessSecret,
						scanDal,
						projectDal,
					)

					result, err := scanAPISer.GetScanProgress(ProjectUuid, TaskId)

					mu.Lock()
					if err != nil {
						errorCount++
						errors = append(errors, err)
						t.Logf("并发实例请求 %d 失败: %v", index+1, err)
					} else {
						successCount++
						if index%3 == 0 { // 每3次打印一次
							t.Logf("并发实例请求 %d 成功: 进度=%d%%", index+1, result.Progress)
						}
					}
					mu.Unlock()
				}(i)
			}

			wg.Wait()
			t.Logf("并发实例测试总结: 成功 %d 次, 失败 %d 次", successCount, errorCount)

			if errorCount > 0 {
				t.Logf("并发实例错误详情:")
				for i, err := range errors {
					t.Logf("  错误 %d: %v", i+1, err)
				}
			}

			convey.Convey("并发实例创建应该有合理的成功率", func() {
				// 并发创建实例可能会有更多问题，但成功率应该超过70%
				successRate := float64(successCount) / 15.0
				t.Logf("并发实例成功率: %.2f%%", successRate*100)
				convey.So(successRate, convey.ShouldBeGreaterThan, 0.7)
			})
		})
	})
}
