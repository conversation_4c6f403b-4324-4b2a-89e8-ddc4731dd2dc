package apitest

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/codesec"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/store"
)

func TestGetScanResult(t *testing.T) {
	convey.Convey("测试 GetScanResult 方法", t, func() {
		// 跳过测试如果环境变量未设置
		if ProjectUuid == "" || TaskId == "" || AccessKey == "" || AccessSecret == "" {
			t.Skip("跳过测试：请设置环境变量 CODESEC_PROJECT_UUID, CODESEC_TASK_ID, CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
			return
		}

		// 创建 ScanAPISer 实例
		scanDal := store.NewCodesecScanDao(nil)
		projectDal := store.NewProjectStore(nil)

		scanAPISer := codesec.NewScanAPISer(
			BaseURL,
			AccessKey,
			AccessSecret,
			scanDal,
			projectDal,
		)

		convey.Convey("当 projectUuid 为空时", func() {
			result, err := scanAPISer.GetScanResult("", TaskId)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "projectUuid cannot be empty")
			convey.So(result, convey.ShouldBeNil)
		})

		convey.Convey("当 taskId 为空时", func() {
			result, err := scanAPISer.GetScanResult(ProjectUuid, "")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "taskId cannot be empty")
			convey.So(result, convey.ShouldBeNil)
		})

		convey.Convey("当参数都正确时", func() {
			result, err := scanAPISer.GetScanResult(ProjectUuid, TaskId)

			convey.Convey("应该成功返回结果", func() {
				if err != nil {
					t.Logf("API 调用失败: %v", err)
					// 如果是鉴权错误，说明签名算法有问题
					if err.Error() == "API error: 鉴权出错" {
						t.Error("检测到鉴权错误，签名算法需要修复")
						convey.So(err, convey.ShouldBeNil) // 这会导致测试失败，提醒需要修复
					} else {
						// 其他错误也记录下来，但不一定是代码问题
						t.Logf("其他错误: %v", err)
						convey.So(err, convey.ShouldBeNil) // 期望没有错误
					}
				} else {
					// 成功的情况
					convey.So(err, convey.ShouldBeNil)
					convey.So(result, convey.ShouldNotBeNil)
					convey.So(result.Status, convey.ShouldBeTrue)
					convey.So(result.Message, convey.ShouldNotBeEmpty)

					// 验证扫描结果数据结构
					convey.So(result.Data.ScanResult.TreeList, convey.ShouldNotBeNil)

					t.Logf("扫描结果消息: %s", result.Message)
					t.Logf("安全漏洞数量: %d", result.Data.ScanResult.SecurityVulNum)
					t.Logf("代码安全弱点数量: %d", result.Data.ScanResult.CodeSecurityWeaknessNum)
					t.Logf("严重漏洞数量: %d", result.Data.ScanResult.CriticalNum)
					t.Logf("高危漏洞数量: %d", result.Data.ScanResult.HighNum)
					t.Logf("中危漏洞数量: %d", result.Data.ScanResult.MediumNum)
					t.Logf("低危漏洞数量: %d", result.Data.ScanResult.LowNum)
					t.Logf("代码行数: %d", result.Data.ScanResult.CodeLineNum)
					t.Logf("编程语言: %s (ID: %d)", result.Data.Language.Name, result.Data.Language.ID)
					t.Logf("漏洞分类树节点数量: %d", len(result.Data.ScanResult.TreeList))

					// 验证漏洞分类树结构
					if len(result.Data.ScanResult.TreeList) > 0 {
						firstNode := result.Data.ScanResult.TreeList[0]
						t.Logf("第一个树节点: Label=%s, Count=%d, AuditCount=%d",
							firstNode.Label, firstNode.Count, firstNode.AuditCount)

						if len(firstNode.Children) > 0 {
							t.Logf("第一个子节点: Label=%s, VulDataId=%s",
								firstNode.Children[0].Label, firstNode.Children[0].VulDataId)
						}
					}
				}
			})
		})

		convey.Convey("当使用无效的 projectUuid 和 taskId 时", func() {
			result, err := scanAPISer.GetScanResult("invalid-project-uuid", "invalid-task-id")

			convey.Convey("应该返回错误", func() {
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(result, convey.ShouldBeNil)
				t.Logf("预期的错误: %v", err)
			})
		})
	})
}
