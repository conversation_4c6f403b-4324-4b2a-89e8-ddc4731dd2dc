# CodeSec API 测试

本目录包含 CodeSec API 的测试用例，使用 GoConvey 测试框架。

## 环境变量设置

在运行测试之前，需要设置以下环境变量：

```bash
export CODESEC_BASE_URL="http://your-codesec-server:28081"
export CODESEC_PROJECT_UUID="your-project-uuid"
export CODESEC_TASK_ID="your-task-id"
export CODESEC_ACCESS_KEY="your-access-key"
export CODESEC_ACCESS_SECRET="your-access-secret"
```

## 运行测试

### 运行所有测试
```bash
go test -v
```

### 运行特定测试
```bash
go test -v -run TestGetScanProgress
```

## 测试说明

### GetScanProgress 测试

测试 `GetScanProgress` 方法的各种情况：

1. **参数验证测试**：
   - 空的 projectUuid
   - 空的 appId

2. **正常调用测试**：
   - 使用有效的参数调用 API
   - 验证返回结果的格式和内容

3. **错误处理测试**：
   - 使用无效的 projectUuid 和 taskId
   - 验证错误处理逻辑

## 注意事项

1. 如果环境变量未设置，相关测试会被跳过
2. 测试会实际调用 CodeSec API，请确保网络连接正常
3. 如果遇到鉴权错误，请检查 AccessKey 和 AccessSecret 是否正确

## 已修复的问题

1. **JSON 字段映射问题**：API 返回的字段名是 `progess`（拼写错误），而不是文档中的 `progress`，已在结构体中修复
2. **签名算法**：使用正确的 V2 API 签名算法，确保鉴权成功
