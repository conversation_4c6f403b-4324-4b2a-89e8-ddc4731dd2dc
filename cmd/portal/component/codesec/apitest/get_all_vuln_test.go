package apitest

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/codesec"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/store"
)

func TestGetAllVuln(t *testing.T) {
	convey.Convey("测试 GetAllVuln 方法", t, func() {
		// 跳过测试如果环境变量未设置
		if ProjectUuid == "" || TaskId == "" || AccessKey == "" || AccessSecret == "" {
			t.Skip("跳过测试：请设置环境变量 CODESEC_PROJECT_UUID, CODESEC_TASK_ID, CODESEC_ACCESS_KEY, CODESEC_ACCESS_SECRET")
			return
		}

		// 创建 ScanAPISer 实例
		scanDal := store.NewCodesecScanDao(nil)
		projectDal := store.NewProjectStore(nil)

		scanAPISer := codesec.NewScanAPISer(
			BaseURL,
			AccessKey,
			AccessSecret,
			scanDal,
			projectDal,
		)

		convey.Convey("当 codesecUuid 为空时", func() {
			vulns, summary, err := scanAPISer.GetAllVuln("", TaskId)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "projectUuid cannot be empty")
			convey.So(vulns, convey.ShouldBeNil)
			convey.So(summary, convey.ShouldBeNil)
		})

		convey.Convey("当 taskId 为空时", func() {
			vulns, summary, err := scanAPISer.GetAllVuln(ProjectUuid, "")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "taskId cannot be empty")
			convey.So(vulns, convey.ShouldBeNil)
			convey.So(summary, convey.ShouldBeNil)
		})

		convey.Convey("当参数都正确时", func() {
			vulns, summary, err := scanAPISer.GetAllVuln(ProjectUuid, TaskId)

			convey.Convey("应该成功返回结果", func() {
				if err != nil {
					t.Logf("API 调用失败: %v", err)
					// 如果是鉴权错误，说明签名算法有问题
					if err.Error() == "API error: 鉴权出错" {
						t.Error("检测到鉴权错误，签名算法需要修复")
						convey.So(err, convey.ShouldBeNil) // 这会导致测试失败，提醒需要修复
					} else {
						// 其他错误也记录下来，但不一定是代码问题
						t.Logf("其他错误: %v", err)
						convey.So(err, convey.ShouldBeNil) // 期望没有错误
					}
				} else {
					// 成功的情况
					convey.So(err, convey.ShouldBeNil)
					convey.So(vulns, convey.ShouldNotBeNil)
					convey.So(summary, convey.ShouldNotBeNil)

					t.Logf("获取到的漏洞数量: %d", len(vulns))
					t.Logf("扫描摘要 - 安全漏洞数量: %d", summary.SecurityVulNum)
					t.Logf("扫描摘要 - 代码安全弱点数量: %d", summary.CodeSecurityWeaknessNum)
					t.Logf("扫描摘要 - 严重漏洞数量: %d", summary.CriticalNum)
					t.Logf("扫描摘要 - 高危漏洞数量: %d", summary.HighNum)
					t.Logf("扫描摘要 - 中危漏洞数量: %d", summary.MediumNum)
					t.Logf("扫描摘要 - 低危漏洞数量: %d", summary.LowNum)
					t.Logf("扫描摘要 - 代码行数: %d", summary.CodeLineNum)

					// 验证漏洞数据结构
					if len(vulns) > 0 {
						firstVuln := vulns[0]
						t.Logf("第一个漏洞信息:")
						t.Logf("  漏洞ID: %s", firstVuln.VulId)
						t.Logf("  漏洞名称: %s", firstVuln.VulName)
						t.Logf("  漏洞类型名称: %s", firstVuln.VulTypeName)
						t.Logf("  文件名: %s", firstVuln.Filename)
						t.Logf("  行号: %s", firstVuln.RowNum)
						t.Logf("  风险等级: %s (ID: %d)", firstVuln.RiskName, firstVuln.RiskId)
						t.Logf("  标签: %s (ID: %d)", firstVuln.TagName, firstVuln.TagId)
						t.Logf("  语言: %s (ID: %d)", firstVuln.LanguageName, firstVuln.LanguageId)

						// 验证必要字段不为空
						convey.So(firstVuln.VulId, convey.ShouldNotBeEmpty)
						convey.So(firstVuln.VulName, convey.ShouldNotBeEmpty)
						convey.So(firstVuln.Filename, convey.ShouldNotBeEmpty)
						convey.So(firstVuln.RiskId, convey.ShouldBeGreaterThan, 0)
						convey.So(firstVuln.TagId, convey.ShouldBeGreaterThan, 0)
					}

					// 验证摘要数据的合理性
					convey.So(summary.SecurityVulNum, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(summary.CodeSecurityWeaknessNum, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(summary.CriticalNum, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(summary.HighNum, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(summary.MediumNum, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(summary.LowNum, convey.ShouldBeGreaterThanOrEqualTo, 0)
					convey.So(summary.CodeLineNum, convey.ShouldBeGreaterThan, 0)
				}
			})
		})

		convey.Convey("当使用无效的 codesecUuid 和 taskId 时", func() {
			vulns, summary, err := scanAPISer.GetAllVuln("invalid-codesec-uuid", "invalid-task-id")

			convey.Convey("应该返回错误", func() {
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(vulns, convey.ShouldBeNil)
				convey.So(summary, convey.ShouldBeNil)
				t.Logf("预期的错误: %v", err)
			})
		})
	})
}
